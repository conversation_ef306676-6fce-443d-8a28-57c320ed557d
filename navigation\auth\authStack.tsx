import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React, { useContext, useEffect } from 'react';
import { Platform } from 'react-native';
import AuthFormScreen from '../../screens/auth/AuthFormScreen';
import AuthPasswordResetScreen from '../../screens/auth/AuthPasswordResetScreen';
import AuthScreen from "../../screens/auth/AuthScreen";
import AuthSignUpFormScreen from '../../screens/auth/AuthSignUpFormScreen';
import { translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';
import useGlobalStore from '../../services/globalState';

const Stack = createNativeStackNavigator();

export default function AuthStack() {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const redirectToSignupAfterLogout = useGlobalStore(state => state.redirectToSignupAfterLogout);
    const setRedirectToSignupAfterLogout = useGlobalStore(state => state.setRedirectToSignupAfterLogout);

    const appTitle: string = translationService.translate("APP_TITLE");

    // Determine initial route based on redirect flag
    const initialRouteName = redirectToSignupAfterLogout ? "AuthSignUpForm" : "Auth";

    // Clear the flag after using it
    useEffect(() => {
        if (redirectToSignupAfterLogout) {
            setRedirectToSignupAfterLogout(false);
        }
    }, [redirectToSignupAfterLogout, setRedirectToSignupAfterLogout]);

    return (
        <Stack.Navigator initialRouteName={initialRouteName}>
            <Stack.Group>
                <Stack.Screen
                    name="Auth"
                    component={AuthScreen}
                    options={{
                        title: appTitle,
                        headerShown: false
                    }}
                />
                <Stack.Screen
                    name="AuthForm"
                    component={AuthFormScreen}
                    options={{
                        title: Platform.OS === "ios" ? "" : appTitle,
                        headerBackTitleVisible: true,
                    }}
                />
                <Stack.Screen
                    name="AuthSignUpForm"
                    component={AuthSignUpFormScreen}
                    options={{
                        title: translationService.translate("SIGN_UP_SCREEN_TITLE"),
                        headerBackTitleVisible: true,
                        headerBackButtonMenuEnabled: true
                    }}
                />
                <Stack.Screen
                    name="AuthPasswordReset"
                    component={AuthPasswordResetScreen}
                    options={{
                        title: translationService.translate("RESET_PASSWORD_SCREEN_TITLE"),
                        headerBackTitleVisible: true,
                        headerBackButtonMenuEnabled: true
                    }}
                />
            </Stack.Group>
            {/* <Stack.Group screenOptions={{ presentation: 'modal', headerBackTitleVisible: true, headerBackButtonMenuEnabled: true }}>
            </Stack.Group> */}
        </Stack.Navigator>
    );
}
