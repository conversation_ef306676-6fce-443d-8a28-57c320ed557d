import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React, { useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import AuthFormScreen from '../../screens/auth/AuthFormScreen';
import AuthPasswordResetScreen from '../../screens/auth/AuthPasswordResetScreen';
import AuthScreen from "../../screens/auth/AuthScreen";
import AuthSignUpFormScreen from '../../screens/auth/AuthSignUpFormScreen';
import { translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

const Stack = createNativeStackNavigator();

export default function AuthStack() {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const [initialRoute, setInitialRoute] = useState<string>("Auth");

    const appTitle: string = translationService.translate("APP_TITLE");

    // Check if user wants to go to signup
    useEffect(() => {
        const checkRedirectFlag = async () => {
            const redirectToSignup = await AsyncStorage.getItem("redirectToSignup");
            if (redirectToSignup === "true") {
                setInitialRoute("AuthSignUpForm");
                // Clear the flag
                await AsyncStorage.removeItem("redirectToSignup");
            }
        };
        checkRedirectFlag();
    }, []);

    return (
        <Stack.Navigator initialRouteName={initialRoute}>
            <Stack.Group>
                <Stack.Screen
                    name="Auth"
                    component={AuthScreen}
                    options={{
                        title: appTitle,
                        headerShown: false
                    }}
                />
                <Stack.Screen
                    name="AuthForm"
                    component={AuthFormScreen}
                    options={{
                        title: Platform.OS === "ios" ? "" : appTitle,
                        headerBackTitleVisible: true,
                    }}
                />
                <Stack.Screen
                    name="AuthSignUpForm"
                    component={AuthSignUpFormScreen}
                    options={{
                        title: translationService.translate("SIGN_UP_SCREEN_TITLE"),
                        headerBackTitleVisible: true,
                        headerBackButtonMenuEnabled: true
                    }}
                />
                <Stack.Screen
                    name="AuthPasswordReset"
                    component={AuthPasswordResetScreen}
                    options={{
                        title: translationService.translate("RESET_PASSWORD_SCREEN_TITLE"),
                        headerBackTitleVisible: true,
                        headerBackButtonMenuEnabled: true
                    }}
                />
            </Stack.Group>
            {/* <Stack.Group screenOptions={{ presentation: 'modal', headerBackTitleVisible: true, headerBackButtonMenuEnabled: true }}>
            </Stack.Group> */}
        </Stack.Navigator>
    );
}
